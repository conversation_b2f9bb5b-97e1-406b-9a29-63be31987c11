package net.summerfarm.wnc.infrastructure.es.repository;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.infrastructure.es.document.CustomFenceArea;
import net.summerfarm.wnc.infrastructure.es.document.GeoShape;
import net.summerfarm.wnc.infrastructure.es.mapper.CustomFenceAreaEsMapper;
import org.elasticsearch.common.geo.ShapeRelation;
import org.elasticsearch.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自定义围栏区域
 * date: 2025/9/3 11:11<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class CustomFenceAreaEsQueryRepositoryImpl implements CustomFenceAreaEsQueryRepository {

    @Resource
    private CustomFenceAreaEsMapper customFenceAreaEsMapper;

    @Override
    public Integer matchEsByAdCodeMsgIdsWithPoi(List<Integer> adCodeMsgIds, String orderPoi) {
        if (adCodeMsgIds == null || adCodeMsgIds.isEmpty() || orderPoi == null) {
            return null;
        }
        String[] split = orderPoi.split(",");
        // 判断POI合法性

        List<CustomFenceArea> customFenceAreas = customFenceAreaEsMapper.selectList(new LambdaEsQueryWrapper<CustomFenceArea>()
                .in(CustomFenceArea::getAdCodeMsgId, adCodeMsgIds)
                .geoShape(CustomFenceArea::getGeoShape, new Point(Double.parseDouble(split[0]), Double.parseDouble(split[1])), ShapeRelation.INTERSECTS)
                .select(CustomFenceArea::getAdCodeMsgId)
        );

        log.info("请求参数: adCodeMsgIds{},orderPoi:{}, 返回结果: {}", JSON.toJSONString(adCodeMsgIds), orderPoi, JSON.toJSONString(customFenceAreas));

        if (CollectionUtils.isEmpty(customFenceAreas)) {
            return null;
        }

        if (customFenceAreas.size() > 1) {
            log.error("\n匹配到多个自定义围栏区域, 请求参数: adCodeMsgIds{},orderPoi:{}, 返回结果: {}", JSON.toJSONString(adCodeMsgIds), orderPoi, JSON.toJSONString(customFenceAreas) + "\n");
        }

        String adCodeMsgId = customFenceAreas.get(0).getAdCodeMsgId();
        if (StringUtils.isEmpty(adCodeMsgId)) {
            return null;
        }

        return Integer.parseInt(adCodeMsgId);
    }

    @Override
    public String findPoiByAdCodeMsgId(Integer adCodeMsgId) {
        if (adCodeMsgId == null) {
            return null;
        }
        List<CustomFenceArea> customFenceAreas = customFenceAreaEsMapper.selectList(new LambdaEsQueryWrapper<CustomFenceArea>()
                .eq(CustomFenceArea::getAdCodeMsgId, adCodeMsgId)
                .select(CustomFenceArea::getGeoShape)
        );

        log.info("请求参数: adCodeMsgId{}, 返回结果: {}", adCodeMsgId, JSON.toJSONString(customFenceAreas.stream().map(CustomFenceArea::getAdCodeMsgId).collect(Collectors.toList())));

        if (CollectionUtils.isEmpty(customFenceAreas)) {
            return null;
        }

        List<GeoShape> geoShapeList = customFenceAreas.stream().map(CustomFenceArea::getGeoShape).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(geoShapeList)) {
            return null;
        }

        return geoShapeList.stream().map(GeoShape::getCoordinates).filter(Objects::nonNull).collect(Collectors.toList()).toString();
    }
}

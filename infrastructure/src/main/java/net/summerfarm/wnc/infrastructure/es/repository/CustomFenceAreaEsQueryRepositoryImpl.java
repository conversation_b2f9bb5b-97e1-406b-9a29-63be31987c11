package net.summerfarm.wnc.infrastructure.es.repository;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.domain.fence.repository.CustomFenceAreaEsQueryRepository;
import net.summerfarm.wnc.infrastructure.es.document.CustomFenceArea;
import net.summerfarm.wnc.infrastructure.es.document.GeoShape;
import net.summerfarm.wnc.infrastructure.es.mapper.CustomFenceAreaEsMapper;
import org.elasticsearch.common.geo.ShapeRelation;
import org.elasticsearch.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自定义围栏区域
 * date: 2025/9/3 11:11<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class CustomFenceAreaEsQueryRepositoryImpl implements CustomFenceAreaEsQueryRepository {

    @Resource
    private CustomFenceAreaEsMapper customFenceAreaEsMapper;

    @Override
    public Integer matchEsByAdCodeMsgIdsWithPoi(List<Integer> adCodeMsgIds, String orderPoi) {
        if (adCodeMsgIds == null || adCodeMsgIds.isEmpty() || orderPoi == null) {
            return null;
        }
        String[] split = orderPoi.split(",");
        // 判断POI合法性
        if (!isValidPoi(orderPoi)) {
            log.warn("无效的POI坐标格式: {}", orderPoi);
            return null;
        }

        List<CustomFenceArea> customFenceAreas = customFenceAreaEsMapper.selectList(new LambdaEsQueryWrapper<CustomFenceArea>()
                .in(CustomFenceArea::getAdCodeMsgId, adCodeMsgIds)
                .geoShape(CustomFenceArea::getGeoShape, new Point(Double.parseDouble(split[0]), Double.parseDouble(split[1])), ShapeRelation.INTERSECTS)
                .select(CustomFenceArea::getAdCodeMsgId)
        );

        log.info("请求参数: adCodeMsgIds{},orderPoi:{}, 返回结果: {}", JSON.toJSONString(adCodeMsgIds), orderPoi, JSON.toJSONString(customFenceAreas));

        if (CollectionUtils.isEmpty(customFenceAreas)) {
            return null;
        }

        if (customFenceAreas.size() > 1) {
            log.error("\n匹配到多个自定义围栏区域, 请求参数: adCodeMsgIds{},orderPoi:{}, 返回结果: {}", JSON.toJSONString(adCodeMsgIds), orderPoi, JSON.toJSONString(customFenceAreas) + "\n");
        }

        String adCodeMsgId = customFenceAreas.get(0).getAdCodeMsgId();
        if (StringUtils.isEmpty(adCodeMsgId)) {
            return null;
        }

        return Integer.parseInt(adCodeMsgId);
    }

    @Override
    public String findPoiByAdCodeMsgId(Integer adCodeMsgId) {
        if (adCodeMsgId == null) {
            return null;
        }
        List<CustomFenceArea> customFenceAreas = customFenceAreaEsMapper.selectList(new LambdaEsQueryWrapper<CustomFenceArea>()
                .eq(CustomFenceArea::getAdCodeMsgId, adCodeMsgId)
                .select(CustomFenceArea::getGeoShape)
        );

        log.info("请求参数: adCodeMsgId{}, 返回结果: {}", adCodeMsgId, JSON.toJSONString(customFenceAreas.stream().map(CustomFenceArea::getAdCodeMsgId).collect(Collectors.toList())));

        if (CollectionUtils.isEmpty(customFenceAreas)) {
            return null;
        }

        List<GeoShape> geoShapeList = customFenceAreas.stream().map(CustomFenceArea::getGeoShape).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(geoShapeList)) {
            return null;
        }

        return geoShapeList.stream().map(GeoShape::getCoordinates).filter(Objects::nonNull).collect(Collectors.toList()).toString();
    }

    /**
     * 验证POI坐标字符串的合法性
     * 格式要求: "经度,纬度" (如: "119.437221,30.089874")
     * 经度范围: -180 到 180
     * 纬度范围: -90 到 90
     *
     * @param orderPoi POI坐标字符串
     * @return true表示合法，false表示不合法
     */
    private boolean isValidPoi(String orderPoi) {
        if (StringUtils.isEmpty(orderPoi)) {
            return false;
        }

        // 去除首尾空格
        orderPoi = orderPoi.trim();

        // 检查是否包含逗号分隔符
        if (!orderPoi.contains(",")) {
            return false;
        }

        // 按逗号分割
        String[] coordinates = orderPoi.split(",");

        // 必须恰好包含两个部分（经度和纬度）
        if (coordinates.length != 2) {
            return false;
        }

        try {
            // 解析经度和纬度
            double longitude = Double.parseDouble(coordinates[0].trim());
            double latitude = Double.parseDouble(coordinates[1].trim());

            // 验证经度范围 (-180 到 180)
            if (longitude < -180.0 || longitude > 180.0) {
                return false;
            }

            // 验证纬度范围 (-90 到 90)
            if (latitude < -90.0 || latitude > 90.0) {
                return false;
            }

            // 检查是否为有效数字（不是NaN或无穷大）
            if (Double.isNaN(longitude) || Double.isInfinite(longitude) ||
                Double.isNaN(latitude) || Double.isInfinite(latitude)) {
                return false;
            }

            return true;
        } catch (NumberFormatException e) {
            // 无法解析为数字
            return false;
        }
    }
}
